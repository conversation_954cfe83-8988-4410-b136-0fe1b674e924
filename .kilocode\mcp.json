{"mcpServers": {"github.com/mrexodia/ida-pro-mcp": {"command": "D:\\Progra~1\\Python311\\python.exe", "args": ["D:\\Progra~1\\Python311\\Lib\\site-packages\\ida_pro_mcp\\server.py"], "timeout": 1800, "disabled": false, "autoApprove": ["check_connection", "get_metadata", "get_function_by_name", "get_function_by_address", "get_current_address", "get_current_function", "convert_number", "list_functions", "list_globals", "list_strings", "decompile_function", "disassemble_function", "get_xrefs_to", "get_xrefs_to_field", "get_entry_points", "set_comment", "rename_local_variable", "rename_global_variable", "set_global_variable_type", "rename_function", "set_function_prototype", "declare_c_type", "set_local_variable_type", "dbg_get_registers", "dbg_get_call_stack", "dbg_list_breakpoints", "dbg_start_process", "dbg_exit_process", "dbg_continue_process", "dbg_run_to", "dbg_set_breakpoint", "dbg_delete_breakpoint", "dbg_enable_breakpoint"], "alwaysAllow": ["check_connection", "get_metadata", "get_function_by_name", "get_function_by_address", "get_current_address", "get_current_function", "convert_number", "list_functions", "list_globals", "list_strings", "decompile_function", "disassemble_function", "get_xrefs_to", "get_xrefs_to_field", "get_entry_points", "set_comment", "rename_local_variable", "rename_global_variable", "set_global_variable_type", "rename_function", "set_function_prototype", "declare_c_type", "set_local_variable_type", "dbg_get_registers", "dbg_get_call_stack", "dbg_list_breakpoints", "dbg_start_process", "dbg_exit_process", "dbg_continue_process", "dbg_run_to", "dbg_set_breakpoint", "dbg_delete_breakpoint", "dbg_enable_breakpoint", "read_file"]}}}