<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kiro 软件下载中心</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .download-card { border: 1px solid #ddd; padding: 15px; margin-bottom: 15px; border-radius: 5px; }
        h1 { color: #333; }
        button { background-color: #4CAF50; color: white; padding: 8px 15px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #45a049; }
        .platform-icon { font-size: 24px; margin-right: 10px; }
    </style>
</head>
<body>
    <h1>Kiro 软件下载</h1>
    <p>当前版本: <span id="version">加载中...</span></p>
    
    <div class="download-card" id="mac-intel">
        <h2><span class="platform-icon">💻</span> macOS (Intel)</h2>
        <button onclick="download('mac-intel')">下载 DMG</button>
    </div>
    
    <div class="download-card" id="mac-arm">
        <h2><span class="platform-icon">🍏</span> macOS (Apple Silicon)</h2>
        <button onclick="download('mac-arm')">下载 DMG</button>
    </div>
    
    <div class="download-card" id="linux">
        <h2><span class="platform-icon">🐧</span> Linux</h2>
        <button onclick="download('linux')">下载 TAR.GZ</button>
        <button onclick="download('linux-deb')">下载 DEB</button>
    </div>
    
    <div class="download-card" id="windows">
        <h2><span class="platform-icon">🪟</span> Windows</h2>
        <button onclick="download('windows')">下载 EXE</button>
    </div>

    <script src="app.js"></script>
</body>
</html>
