<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kiro 软件下载中心 - 跨平台应用程序官方下载</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="Kiro 官方下载中心，支持 Windows、macOS、Linux 多平台。免费下载最新版本的 Kiro 应用程序，体验强大的功能和优秀的用户体验。">
    <meta name="keywords" content="Kiro, 软件下载, Windows, macOS, Linux, 跨平台, 应用程序, 免费下载">
    <meta name="author" content="Kiro Team">
    <meta name="robots" content="index, follow">
    <meta name="language" content="zh-CN">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Kiro 软件下载中心 - 跨平台应用程序官方下载">
    <meta property="og:description" content="Kiro 官方下载中心，支持 Windows、macOS、Linux 多平台。免费下载最新版本的 Kiro 应用程序。">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://download.kiro.dev">
    <meta property="og:image" content="https://download.kiro.dev/images/kiro-og-image.png">
    <meta property="og:site_name" content="Kiro Download Center">
    <meta property="og:locale" content="zh_CN">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Kiro 软件下载中心">
    <meta name="twitter:description" content="免费下载 Kiro 应用程序，支持 Windows、macOS、Linux 多平台">
    <meta name="twitter:image" content="https://download.kiro.dev/images/kiro-twitter-card.png">

    <!-- Additional Meta Tags -->
    <meta name="theme-color" content="#667eea">
    <meta name="msapplication-TileColor" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Kiro Download">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://download.kiro.dev">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "Kiro",
        "description": "跨平台应用程序，支持 Windows、macOS、Linux 多平台",
        "url": "https://download.kiro.dev",
        "downloadUrl": "https://download.kiro.dev",
        "operatingSystem": ["Windows", "macOS", "Linux"],
        "applicationCategory": "UtilityApplication",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Kiro Team"
        }
    }
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .version-info {
            text-align: center;
            margin-bottom: 40px;
            font-size: 1.1rem;
            color: #666;
        }

        #version {
            font-weight: 600;
            color: #667eea;
        }

        .loading {
            display: inline-block;
            animation: pulse 1.5s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .download-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .changelog-section {
            margin-top: 40px;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .changelog-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 15px;
        }

        .changelog-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }

        .toggle-changelog {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            min-width: auto;
        }

        .toggle-changelog:hover {
            background: #667eea;
            color: white;
            transform: none;
            box-shadow: none;
        }

        .changelog-content {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .changelog-content.show {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .version-item {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .version-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .version-number {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .version-date {
            color: #666;
            font-size: 0.9rem;
        }

        .version-changes {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .version-changes li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
            color: #555;
        }

        .version-changes li::before {
            content: '•';
            color: #667eea;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .change-type {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-right: 8px;
        }

        .change-type.new {
            background: #e8f5e8;
            color: #2d5a2d;
        }

        .change-type.fix {
            background: #fff3cd;
            color: #856404;
        }

        .change-type.improve {
            background: #d1ecf1;
            color: #0c5460;
        }

        .stats-info {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 10px;
            font-size: 0.9rem;
            color: #666;
        }

        .download-count {
            display: inline-block;
            margin-left: 8px;
            padding: 2px 8px;
            background: rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            color: #667eea;
        }

        .download-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .download-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .download-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .download-card.recommended {
            border-color: #667eea;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
        }

        .download-card.recommended::after {
            content: '推荐';
            position: absolute;
            top: 15px;
            right: 15px;
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .platform-icon {
            font-size: 2.5rem;
            margin-right: 15px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .card-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }

        .card-subtitle {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }

        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            min-width: 120px;
        }

        button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        button:hover::before {
            left: 100%;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        button:active {
            transform: translateY(0);
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            background: #fee;
            border: 1px solid #fcc;
            color: #c33;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            display: none;
        }

        .success-message {
            background: #efe;
            border: 1px solid #cfc;
            color: #3c3;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            h1 {
                font-size: 2rem;
            }

            .download-grid {
                grid-template-columns: 1fr;
            }

            .download-card {
                padding: 20px;
            }

            .button-group {
                flex-direction: column;
            }

            button {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            .container {
                padding: 15px;
            }

            h1 {
                font-size: 1.8rem;
            }

            .platform-icon {
                font-size: 2rem;
            }

            .card-title {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Kiro 软件下载中心</h1>
        <div class="version-info">
            当前版本: <span id="version" class="loading">加载中...</span>
        </div>

        <div id="error-message" class="error-message"></div>
        <div id="success-message" class="success-message"></div>

        <div class="download-grid">
            <div class="download-card" id="mac-intel">
                <div class="card-header">
                    <span class="platform-icon">💻</span>
                    <div>
                        <h2 class="card-title">macOS (Intel)</h2>
                        <div class="card-subtitle">适用于 Intel 处理器的 Mac</div>
                    </div>
                </div>
                <div class="button-group">
                    <button type="button" onclick="download('mac-intel')">下载 DMG<span class="download-count" id="count-mac-intel">0</span></button>
                </div>
            </div>

            <div class="download-card" id="mac-arm">
                <div class="card-header">
                    <span class="platform-icon">🍏</span>
                    <div>
                        <h2 class="card-title">macOS (Apple Silicon)</h2>
                        <div class="card-subtitle">适用于 M1/M2/M3 处理器的 Mac</div>
                    </div>
                </div>
                <div class="button-group">
                    <button type="button" onclick="download('mac-arm')">下载 DMG<span class="download-count" id="count-mac-arm">0</span></button>
                </div>
            </div>

            <div class="download-card" id="linux">
                <div class="card-header">
                    <span class="platform-icon">🐧</span>
                    <div>
                        <h2 class="card-title">Linux</h2>
                        <div class="card-subtitle">支持各种 Linux 发行版</div>
                    </div>
                </div>
                <div class="button-group">
                    <button type="button" onclick="download('linux')">下载 TAR.GZ<span class="download-count" id="count-linux">0</span></button>
                    <button type="button" onclick="download('linux-deb')">下载 DEB<span class="download-count" id="count-linux-deb">0</span></button>
                </div>
            </div>

            <div class="download-card" id="windows">
                <div class="card-header">
                    <span class="platform-icon">🪟</span>
                    <div>
                        <h2 class="card-title">Windows</h2>
                        <div class="card-subtitle">Windows 10/11 64位</div>
                    </div>
                </div>
                <div class="button-group">
                    <button type="button" onclick="download('windows')">下载 EXE<span class="download-count" id="count-windows">0</span></button>
                </div>
            </div>
        </div>

        <div class="changelog-section">
            <div class="changelog-header">
                <h3 class="changelog-title">版本历史</h3>
                <button type="button" class="toggle-changelog" onclick="toggleChangelog()">
                    <span id="changelog-toggle-text">显示</span>
                </button>
            </div>
            <div class="changelog-content" id="changelog-content">
                <div id="changelog-list">
                    <!-- 版本历史将通过JavaScript动态加载 -->
                    <div class="version-item">
                        <div class="version-header">
                            <span class="version-number">加载中...</span>
                            <span class="version-date"></span>
                        </div>
                        <ul class="version-changes">
                            <li>正在获取版本历史信息...</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
