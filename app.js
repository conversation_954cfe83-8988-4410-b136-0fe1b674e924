// 元数据URL
const METADATA_URLS = {
    'mac-intel': 'https://prod.download.desktop.kiro.dev/stable/metadata-dmg-darwin-x64-stable.json',
    'mac-arm': 'https://prod.download.desktop.kiro.dev/stable/metadata-dmg-darwin-arm64-stable.json',
    'linux': 'https://prod.download.desktop.kiro.dev/stable/metadata-linux-x64-stable.json',
    'linux-deb': 'https://prod.download.desktop.kiro.dev/stable/metadata-linux-x64-deb-stable.json',
    'windows': 'https://prod.download.desktop.kiro.dev/stable/metadata-win32-x64-user-stable.json'
};

let downloadLinks = {};
let currentVersion = '';
let downloadStats = {};

// 显示消息的辅助函数
function showMessage(message, type = 'info', duration = 3000) {
    const errorEl = document.getElementById('error-message');
    const successEl = document.getElementById('success-message');

    // 隐藏所有消息
    errorEl.style.display = 'none';
    successEl.style.display = 'none';

    if (type === 'error') {
        errorEl.textContent = message;
        errorEl.style.display = 'block';
    } else if (type === 'success') {
        successEl.textContent = message;
        successEl.style.display = 'block';
    }

    // 自动隐藏消息
    if (duration > 0) {
        setTimeout(() => {
            errorEl.style.display = 'none';
            successEl.style.display = 'none';
        }, duration);
    }
}

// 更新版本显示
function updateVersionDisplay(version) {
    const versionEl = document.getElementById('version');
    versionEl.textContent = version;
    versionEl.classList.remove('loading');
}

// 设置按钮状态
function setButtonsEnabled(enabled) {
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        button.disabled = !enabled;
    });
}

// 获取所有元数据
async function fetchAllMetadata() {
    try {
        setButtonsEnabled(false);
        showMessage('正在获取最新版本信息...', 'info', 0);

        const responses = await Promise.all(
            Object.keys(METADATA_URLS).map(async (key) => {
                const response = await fetch(METADATA_URLS[key]);
                if (!response.ok) {
                    throw new Error(`获取 ${key} 元数据失败: ${response.status}`);
                }
                return response.json();
            })
        );

        // 处理响应
        downloadLinks['mac-intel'] = responses[0].url;
        downloadLinks['mac-arm'] = responses[1].url;
        downloadLinks['linux'] = responses[2].files?.find(f => f.url.endsWith('.tar.gz'))?.url;
        downloadLinks['linux-deb'] = responses[3].files?.find(f => f.url.endsWith('.deb'))?.url;
        downloadLinks['windows'] = responses[4].url;

        // 验证所有下载链接
        const missingLinks = Object.keys(downloadLinks).filter(key => !downloadLinks[key]);
        if (missingLinks.length > 0) {
            console.warn('缺少下载链接:', missingLinks);
        }

        currentVersion = responses[0].version;
        updateVersionDisplay(currentVersion);

        setButtonsEnabled(true);
        showMessage('版本信息获取成功！', 'success');

        // 检测用户平台并高亮推荐选项
        detectAndHighlightPlatform();

    } catch (error) {
        console.error('获取元数据失败:', error);
        updateVersionDisplay('获取版本失败');
        showMessage(`获取版本信息失败: ${error.message}。请检查网络连接后刷新页面重试。`, 'error', 0);
        setButtonsEnabled(false);
    }
}

// 检测用户平台
function detectUserPlatform() {
    const userAgent = navigator.userAgent.toLowerCase();
    const platform = navigator.platform.toLowerCase();

    if (platform.includes('mac') || userAgent.includes('mac')) {
        // 检测是否为 Apple Silicon
        if (userAgent.includes('arm') || platform.includes('arm')) {
            return 'mac-arm';
        }
        return 'mac-intel';
    } else if (platform.includes('linux') || userAgent.includes('linux')) {
        return 'linux';
    } else if (platform.includes('win') || userAgent.includes('windows')) {
        return 'windows';
    }

    return null;
}

// 高亮推荐的平台
function detectAndHighlightPlatform() {
    const detectedPlatform = detectUserPlatform();
    if (detectedPlatform) {
        const card = document.getElementById(detectedPlatform);
        if (card) {
            card.classList.add('recommended');
            console.log(`检测到平台: ${detectedPlatform}，已高亮推荐选项`);
        }
    }
}

// 记录下载统计
function recordDownload(platform) {
    if (!downloadStats[platform]) {
        downloadStats[platform] = 0;
    }
    downloadStats[platform]++;

    // 保存到本地存储
    try {
        localStorage.setItem('kiro-download-stats', JSON.stringify(downloadStats));
    } catch (e) {
        console.warn('无法保存下载统计:', e);
    }

    // 更新显示
    updateDownloadStatsDisplay();
}

// 加载下载统计
function loadDownloadStats() {
    try {
        const saved = localStorage.getItem('kiro-download-stats');
        if (saved) {
            downloadStats = JSON.parse(saved);
        }
    } catch (e) {
        console.warn('无法加载下载统计:', e);
        downloadStats = {};
    }

    // 更新显示
    updateDownloadStatsDisplay();
}

// 更新下载统计显示
function updateDownloadStatsDisplay() {
    const platforms = ['mac-intel', 'mac-arm', 'linux', 'linux-deb', 'windows'];

    platforms.forEach(platform => {
        const countEl = document.getElementById(`count-${platform}`);
        if (countEl) {
            const count = downloadStats[platform] || 0;
            countEl.textContent = count > 0 ? count : '';
            countEl.style.display = count > 0 ? 'inline-block' : 'none';
        }
    });
}

// 下载函数
function download(platform) {
    if (!downloadLinks[platform]) {
        showMessage('下载链接未准备好，请稍后再试', 'error');
        return;
    }

    try {
        // 记录下载统计
        recordDownload(platform);

        // 显示下载开始消息
        showMessage('正在开始下载...', 'success', 2000);

        // 打开下载链接
        const link = document.createElement('a');
        link.href = downloadLinks[platform];
        link.target = '_blank';
        link.rel = 'noopener noreferrer';

        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log(`开始下载 ${platform}:`, downloadLinks[platform]);

    } catch (error) {
        console.error('下载失败:', error);
        showMessage('下载失败，请重试', 'error');
    }
}

// 重试获取元数据
function retryFetchMetadata() {
    showMessage('正在重新获取版本信息...', 'info');
    fetchAllMetadata();
}

// 页面加载时的初始化
function initialize() {
    loadDownloadStats();
    fetchAllMetadata();

    // 添加重试按钮（如果需要）
    const versionEl = document.getElementById('version');
    versionEl.addEventListener('click', () => {
        if (versionEl.textContent.includes('失败')) {
            retryFetchMetadata();
        }
    });
}

// 模拟版本历史数据（实际项目中应该从API获取）
const VERSION_HISTORY = [
    {
        version: "1.2.3",
        date: "2024-01-15",
        changes: [
            { type: "new", text: "新增深色模式支持" },
            { type: "new", text: "添加快捷键自定义功能" },
            { type: "improve", text: "优化启动速度，提升30%性能" },
            { type: "fix", text: "修复在高分辨率显示器上的显示问题" },
            { type: "fix", text: "解决某些情况下崩溃的问题" }
        ]
    },
    {
        version: "1.2.2",
        date: "2024-01-01",
        changes: [
            { type: "improve", text: "改进用户界面响应速度" },
            { type: "fix", text: "修复文件同步问题" },
            { type: "fix", text: "解决内存泄漏问题" }
        ]
    },
    {
        version: "1.2.1",
        date: "2023-12-20",
        changes: [
            { type: "new", text: "支持拖拽文件功能" },
            { type: "improve", text: "优化网络连接稳定性" },
            { type: "fix", text: "修复登录状态异常问题" }
        ]
    },
    {
        version: "1.2.0",
        date: "2023-12-01",
        changes: [
            { type: "new", text: "全新的用户界面设计" },
            { type: "new", text: "添加插件系统支持" },
            { type: "improve", text: "重构核心架构，提升稳定性" },
            { type: "fix", text: "修复多个已知问题" }
        ]
    }
];

// 切换版本历史显示
function toggleChangelog() {
    const content = document.getElementById('changelog-content');
    const toggleText = document.getElementById('changelog-toggle-text');

    if (content.classList.contains('show')) {
        content.classList.remove('show');
        toggleText.textContent = '显示';
    } else {
        content.classList.add('show');
        toggleText.textContent = '隐藏';

        // 如果是第一次显示，加载版本历史
        if (!content.dataset.loaded) {
            loadVersionHistory();
            content.dataset.loaded = 'true';
        }
    }
}

// 加载版本历史
function loadVersionHistory() {
    const changelogList = document.getElementById('changelog-list');

    // 清空加载中的内容
    changelogList.innerHTML = '';

    VERSION_HISTORY.forEach(version => {
        const versionItem = document.createElement('div');
        versionItem.className = 'version-item';

        const changesHtml = version.changes.map(change =>
            `<li><span class="change-type ${change.type}">${getChangeTypeText(change.type)}</span>${change.text}</li>`
        ).join('');

        versionItem.innerHTML = `
            <div class="version-header">
                <span class="version-number">v${version.version}</span>
                <span class="version-date">${formatDate(version.date)}</span>
            </div>
            <ul class="version-changes">
                ${changesHtml}
            </ul>
        `;

        changelogList.appendChild(versionItem);
    });
}

// 获取变更类型文本
function getChangeTypeText(type) {
    const typeMap = {
        'new': '新增',
        'fix': '修复',
        'improve': '改进'
    };
    return typeMap[type] || type;
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// 页面加载时获取数据
window.addEventListener('DOMContentLoaded', initialize);
