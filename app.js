// 元数据URL
const METADATA_URLS = {
    'mac-intel': 'https://prod.download.desktop.kiro.dev/stable/metadata-dmg-darwin-x64-stable.json',
    'mac-arm': 'https://prod.download.desktop.kiro.dev/stable/metadata-dmg-darwin-arm64-stable.json',
    'linux': 'https://prod.download.desktop.kiro.dev/stable/metadata-linux-x64-stable.json',
    'linux-deb': 'https://prod.download.desktop.kiro.dev/stable/metadata-linux-x64-deb-stable.json',
    'windows': 'https://prod.download.desktop.kiro.dev/stable/metadata-win32-x64-user-stable.json'
};

let downloadLinks = {};
let currentVersion = '';

// 获取所有元数据
async function fetchAllMetadata() {
    try {
        const responses = await Promise.all(
            Object.keys(METADATA_URLS).map(key => 
                fetch(METADATA_URLS[key]).then(res => res.json())
            )
        );
        
        // 处理响应
        downloadLinks['mac-intel'] = responses[0].url;
        downloadLinks['mac-arm'] = responses[1].url;
        downloadLinks['linux'] = responses[2].files.find(f => f.url.endsWith('.tar.gz')).url;
        downloadLinks['linux-deb'] = responses[3].files.find(f => f.url.endsWith('.deb')).url;
        downloadLinks['windows'] = responses[4].url;
        
        currentVersion = responses[0].version;
        document.getElementById('version').textContent = currentVersion;
        
    } catch (error) {
        console.error('获取元数据失败:', error);
        document.getElementById('version').textContent = '获取版本失败，请刷新重试';
    }
}

// 下载函数
function download(platform) {
    if (!downloadLinks[platform]) {
        alert('下载链接未准备好，请稍后再试');
        return;
    }
    window.open(downloadLinks[platform], '_blank');
}

// 页面加载时获取数据
window.addEventListener('DOMContentLoaded', fetchAllMetadata);
